cmake 3.30.3
curl 44fcb024e4965307f62e5594071eb7a813eb942ebaf7b7c6035c0514bf2b1285
features core;net;tiff
fix-proj4-targets-cmake.patch d92803e2b1aeaf5b0218b8c6a4de4ad6f49ec502f4bd7c641fef31face6f7dac
fix-win-output-name.patch a73a3720dd596992ab1bbe4ea9c32e42e1d13cf3069682ba70c7ee63b289c650
nlohmann-json 0ac39ae349077b56a798d820e87e573b4628d7ca083a991f8b148d435c7dcf68
portfile.cmake 9ba219c77615773af1ed82aa5d02389be80b1c518dff97db07c80ebea33c9073
ports.cmake 3855df80ecd84038296b7ae73271a6190f21c4af0bb1a0ca0be752a308aaa53d
post_build_checks 2
powershell 7.4.5
remove_toolset_restriction.patch b21e0c56702cb1cd8b2bc07cc85a4b2bc15e23a9635fae37cd0c87d6b8306b83
sqlite3 d48a29090f51f0929e5c113a3593b9fb30c4617401bfa2837f101dd2bea8e356
tiff a64ebc6de91f4e765613bd56ce19b706ce4e83f2294cf20c4bdeab1f137065d4
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-0433e2303d8e9448dd9cf29d3052811cb89f6324bfd45703cfd4ec1e4134228d-3361a17d8afbd07b0f5c7f4dac4bd892b23a29d3
usage 6e599122e9ee03cfe3a611bca09d2a5ba351fc74c901cf8dacf45536f87b58bd
vcpkg-cmake 33962a5a157617ff0353b7dab305f2ed500ed37d8fe8e1fa7e30987308031f29
vcpkg-cmake-config 7a35022a61d2a358cbd8a12979842b715f77928ae06ab502b3d30e5663bff295
vcpkg.json 281fd43344ed6bc70f7ece4c7cc424f9aa7691c0ef5b87ac191022aa5f162eee
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_copy_pdbs d57e4f196c82dc562a9968c6155073094513c31e2de475694143d3aa47954b1c
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github b743742296a114ea1b18ae99672e02f142c4eb2bef7f57d36c038bedbfb0502f
vcpkg_list f5de3ebcbc40a4db90622ade9aca918e2cf404dc0d91342fcde457d730e6fa29
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
