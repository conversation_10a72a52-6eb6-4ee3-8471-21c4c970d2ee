{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/proj-x64-windows-9.3.1-58b7c5ee-392b-4789-95db-c54ea20cfc43", "name": "proj:x64-windows@9.3.1 8de977d553cc623b8f96f94bbb77535f649c5614cfbd21d8c82beba343225ef3", "creationInfo": {"creators": ["Tool: vcpkg-2024-08-01-fd884a0d390d12783076341bd43d77c3a6a15658"], "created": "2024-09-16T17:33:22Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-5"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-5", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "proj", "SPDXID": "SPDXRef-port", "versionInfo": "9.3.1", "downloadLocation": "NOASSERTION", "homepage": "https://proj.org/", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "PROJ library for cartographic projections", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "proj:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "8de977d553cc623b8f96f94bbb77535f649c5614cfbd21d8c82beba343225ef3", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-1", "name": "OSGeo/PROJ", "downloadLocation": "git+https://github.com/OSGeo/PROJ@9.3.1", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "45775e2b2a6b5bc490743c562155521a2ef48c5a8834cc96f88784aea785df10688f8962ae22fcac64d3b2f85378539ef1d3a082243cdc0ca3695ed8b9efa18b"}]}], "files": [{"fileName": "./D:/a/fiona-wheels/fiona-wheels/ports/proj/fix-proj4-targets-cmake.patch", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "d92803e2b1aeaf5b0218b8c6a4de4ad6f49ec502f4bd7c641fef31face6f7dac"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/a/fiona-wheels/fiona-wheels/ports/proj/fix-win-output-name.patch", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "a73a3720dd596992ab1bbe4ea9c32e42e1d13cf3069682ba70c7ee63b289c650"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/a/fiona-wheels/fiona-wheels/ports/proj/portfile.cmake", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "9ba219c77615773af1ed82aa5d02389be80b1c518dff97db07c80ebea33c9073"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/a/fiona-wheels/fiona-wheels/ports/proj/remove_toolset_restriction.patch", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "b21e0c56702cb1cd8b2bc07cc85a4b2bc15e23a9635fae37cd0c87d6b8306b83"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/a/fiona-wheels/fiona-wheels/ports/proj/usage", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "6e599122e9ee03cfe3a611bca09d2a5ba351fc74c901cf8dacf45536f87b58bd"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./D:/a/fiona-wheels/fiona-wheels/ports/proj/vcpkg.json", "SPDXID": "SPDXRef-file-5", "checksums": [{"algorithm": "SHA256", "checksumValue": "281fd43344ed6bc70f7ece4c7cc424f9aa7691c0ef5b87ac191022aa5f162eee"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}