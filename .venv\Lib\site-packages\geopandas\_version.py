
# This file was generated by 'versioneer.py' (0.29) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2025-06-27T07:02:14+1000",
 "dirty": false,
 "error": null,
 "full-revisionid": "e9b58ce57a238b28ebf5eddd83d437db92c314b5",
 "version": "1.1.1"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
