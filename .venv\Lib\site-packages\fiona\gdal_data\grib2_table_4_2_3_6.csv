"subcat","short_name","name","unit","unit_conv"
-4,"######################################################################################################","#","#","#"
-3,"DO NOT MODIFY THIS FILE. It is generated by frmts/grib/degrib/merge_degrib_and_wmo_tables.py","#","#","#"
-2,"from tables at version https://github.com/wmo-im/GRIB2/commit/cf3a2a24695f60f64ac9d5eb26a24b26d2a8a816","#","#","#"
-1,"######################################################################################################","#","#","#"
0,"","Global solar irradiance","W m-2","UC_NONE"
1,"","Global solar exposure","J m-2","UC_NONE"
2,"","Direct solar irradiance","W m-2","UC_NONE"
3,"","Direct solar exposure","J m-2","UC_NONE"
4,"","Diffuse solar irradiance","W m-2","UC_NONE"
5,"","Diffuse solar exposure","J m-2","UC_NONE"
