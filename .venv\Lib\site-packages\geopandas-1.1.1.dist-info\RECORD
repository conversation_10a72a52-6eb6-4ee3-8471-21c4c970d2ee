geopandas-1.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
geopandas-1.1.1.dist-info/METADATA,sha256=ifBPRAYAMSRaa5IcbdEMLkvglloaWxC35--YEzVaOKo,2253
geopandas-1.1.1.dist-info/RECORD,,
geopandas-1.1.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
geopandas-1.1.1.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
geopandas-1.1.1.dist-info/licenses/LICENSE.txt,sha256=xg76FZN6WoKlonhqJr50UpNv_-x6hFck2wdA5UU1yOo,1498
geopandas-1.1.1.dist-info/top_level.txt,sha256=L_hA4DbHsRJmcvNncFdVSeojIfuyaIX9f3dRJ2T0hQA,10
geopandas/__init__.py,sha256=xljJeGLTAa-yH8LLDx35qKfMijAbuLQQG0uot4Qyims,894
geopandas/__pycache__/__init__.cpython-313.pyc,,
geopandas/__pycache__/_compat.cpython-313.pyc,,
geopandas/__pycache__/_config.cpython-313.pyc,,
geopandas/__pycache__/_decorator.cpython-313.pyc,,
geopandas/__pycache__/_version.cpython-313.pyc,,
geopandas/__pycache__/accessors.cpython-313.pyc,,
geopandas/__pycache__/array.cpython-313.pyc,,
geopandas/__pycache__/base.cpython-313.pyc,,
geopandas/__pycache__/conftest.cpython-313.pyc,,
geopandas/__pycache__/explore.cpython-313.pyc,,
geopandas/__pycache__/geodataframe.cpython-313.pyc,,
geopandas/__pycache__/geoseries.cpython-313.pyc,,
geopandas/__pycache__/plotting.cpython-313.pyc,,
geopandas/__pycache__/sindex.cpython-313.pyc,,
geopandas/__pycache__/testing.cpython-313.pyc,,
geopandas/_compat.py,sha256=k7J_zWvX-EW-rBbtSAeOIuYpNHbA0I40nEEK9P0Lmpw,2532
geopandas/_config.py,sha256=w8amy6DMAFUPfiJa1cBtdX6NUrDE3ld0H2Sz1MSyOc8,3866
geopandas/_decorator.py,sha256=vsDDZH05tFdT79Ywl6pU9ZipWgPMdJU95ehQA_wd8A0,2019
geopandas/_version.py,sha256=oES7cR2LJvS4ZPQ7_nEEW0QyArbde5r8cIHG7fkquU8,497
geopandas/accessors.py,sha256=O04hZC7m4E_6tVOOK-9JwYv4qm4aQ_yN7pGmSl58UHU,750
geopandas/array.py,sha256=jKfJEL5mAZ-07p8gE-ZzQOaVInC3xBx58HzxvhU9TYY,64179
geopandas/base.py,sha256=tF5sYbvTWQheaBqaO8JRVSbrINCLb4BPV3QU8RWKqCE,234062
geopandas/conftest.py,sha256=C_nsiUywnW_6HAyIOklinbTHbD7aKKGSkBY5BIMjgmg,1374
geopandas/datasets/__init__.py,sha256=pxIa7JvJv5kFajD632CZzbL-_cSqpI0yG4ttTx8ILQg,1012
geopandas/datasets/__pycache__/__init__.cpython-313.pyc,,
geopandas/explore.py,sha256=MLHahDdOE9I0A1DEIojHeZsLlEk3u6uAYGT9-Rh_PtU,37179
geopandas/geodataframe.py,sha256=oWJYY18kWmenVkAMhE_qcX3FU9dLdZbIDU0YbFpLN68,112043
geopandas/geoseries.py,sha256=XqtEqgArILIMaC4z66otyjyn192Ggz98OP2M0MggAHE,53455
geopandas/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
geopandas/io/__pycache__/__init__.cpython-313.pyc,,
geopandas/io/__pycache__/_geoarrow.cpython-313.pyc,,
geopandas/io/__pycache__/_pyarrow_hotfix.cpython-313.pyc,,
geopandas/io/__pycache__/arrow.cpython-313.pyc,,
geopandas/io/__pycache__/file.cpython-313.pyc,,
geopandas/io/__pycache__/sql.cpython-313.pyc,,
geopandas/io/__pycache__/util.cpython-313.pyc,,
geopandas/io/_geoarrow.py,sha256=LSMOsxd8LZzSPlox29lOxv7TVIaYQ23gFun_zALdBgg,21712
geopandas/io/_pyarrow_hotfix.py,sha256=_bB-IREMLQOkKY4QRryJZgMqwswL7Y_rC6xAc_AClWg,2354
geopandas/io/arrow.py,sha256=uvc1UdyN6BzZbge9Mm04x_1k8EyPr4us8L013eZK24g,32547
geopandas/io/file.py,sha256=xPcd22xaTPWLHp26Z817n3Eapc7lm4k-SEKXreOUQ1I,32925
geopandas/io/sql.py,sha256=hZ88fSItLtVbmONDxKbqVFBYGODiDaKHNLotdvIRzBU,15440
geopandas/io/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
geopandas/io/tests/__pycache__/__init__.cpython-313.pyc,,
geopandas/io/tests/__pycache__/generate_legacy_storage_files.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_arrow.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_file.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_file_geom_types_drivers.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_geoarrow.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_infer_schema.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_pickle.cpython-313.pyc,,
geopandas/io/tests/__pycache__/test_sql.cpython-313.pyc,,
geopandas/io/tests/generate_legacy_storage_files.py,sha256=id96XdPzVchZ3cDm9-JynH4DG3TiRvldqblDcPDtzgo,2722
geopandas/io/tests/test_arrow.py,sha256=bkLyutJFduYj_ab2jTCIW32EfjWK2ILA5lJcKbfvHO0,45156
geopandas/io/tests/test_file.py,sha256=qKj4Qg6Q6PQh3bZW-ef8PnKNS6iXmOK1PX6_D3PKxLo,54271
geopandas/io/tests/test_file_geom_types_drivers.py,sha256=m4wolxh3T5jfDgyCB8pdTypAGaKt5wUC-n30uVozf4k,9165
geopandas/io/tests/test_geoarrow.py,sha256=HrLUoMkyIas96rxaK2Ha92JMPh9ba4KwjpoYldqgrzA,20885
geopandas/io/tests/test_infer_schema.py,sha256=GcGUL9KRI9dYIpgaapS2ew2RTvma43twGdymn1Q2QUg,8501
geopandas/io/tests/test_pickle.py,sha256=Gjl3iUXnU_l0gT1NCDtf6PItnmrmCFYoF5DjpVhHbnM,1399
geopandas/io/tests/test_sql.py,sha256=aqNNv6ByBnJK8pgClGrRjlUtuMVge8s7jL3ZVnRCCIo,30226
geopandas/io/util.py,sha256=nNis4hnIiD5ipThcGGBXn81cL0vJwrS8swJOJ5461vo,2965
geopandas/plotting.py,sha256=7oPetWypQWCVZGerqrsP0VgDb_0rYfJ2bz-fEuJ5E18,34039
geopandas/sindex.py,sha256=IM4DOEEabUXWuEIT4d5zN-dHlV563hBg9H2IJt9YL2U,21899
geopandas/testing.py,sha256=y0UjIaFKYcjgSRJORYlsVdyjRGsiFidErgWbPjmqK4I,11072
geopandas/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
geopandas/tests/__pycache__/__init__.cpython-313.pyc,,
geopandas/tests/__pycache__/test_api.cpython-313.pyc,,
geopandas/tests/__pycache__/test_array.cpython-313.pyc,,
geopandas/tests/__pycache__/test_compat.cpython-313.pyc,,
geopandas/tests/__pycache__/test_config.cpython-313.pyc,,
geopandas/tests/__pycache__/test_crs.cpython-313.pyc,,
geopandas/tests/__pycache__/test_datasets.cpython-313.pyc,,
geopandas/tests/__pycache__/test_decorator.cpython-313.pyc,,
geopandas/tests/__pycache__/test_dissolve.cpython-313.pyc,,
geopandas/tests/__pycache__/test_explore.cpython-313.pyc,,
geopandas/tests/__pycache__/test_extension_array.cpython-313.pyc,,
geopandas/tests/__pycache__/test_geocode.cpython-313.pyc,,
geopandas/tests/__pycache__/test_geodataframe.cpython-313.pyc,,
geopandas/tests/__pycache__/test_geom_methods.cpython-313.pyc,,
geopandas/tests/__pycache__/test_geoseries.cpython-313.pyc,,
geopandas/tests/__pycache__/test_merge.cpython-313.pyc,,
geopandas/tests/__pycache__/test_op_output_types.cpython-313.pyc,,
geopandas/tests/__pycache__/test_overlay.cpython-313.pyc,,
geopandas/tests/__pycache__/test_pandas_accessor.cpython-313.pyc,,
geopandas/tests/__pycache__/test_pandas_methods.cpython-313.pyc,,
geopandas/tests/__pycache__/test_plotting.cpython-313.pyc,,
geopandas/tests/__pycache__/test_show_versions.cpython-313.pyc,,
geopandas/tests/__pycache__/test_sindex.cpython-313.pyc,,
geopandas/tests/__pycache__/test_testing.cpython-313.pyc,,
geopandas/tests/__pycache__/test_types.cpython-313.pyc,,
geopandas/tests/__pycache__/util.cpython-313.pyc,,
geopandas/tests/data/null_geom.geojson,sha256=Xof4mv2lVaGx1D2dwShkFp1tAUmk8iK-EtQKaoatgGY,506
geopandas/tests/test_api.py,sha256=0EUrUuj7dhlTFiRlh9u6BlWRDb9aul70B_e8rvas_H0,1123
geopandas/tests/test_array.py,sha256=-oFpS4JvhW3lVY-KL8N8NdU1hVOleJI0Cr2vYL3j59U,30424
geopandas/tests/test_compat.py,sha256=YPEZtNUxJax4NZpJ5HRD9mr5PHrpPKA7K4HpVECQi0E,923
geopandas/tests/test_config.py,sha256=c19lPC70k1usUrpFdeRT2piNo3c8jJOY49pdQD67QcA,1204
geopandas/tests/test_crs.py,sha256=XUKxRHLI1J2O9EQl2jivDAtpvZpgTHMC0JsUtMZljXY,26171
geopandas/tests/test_datasets.py,sha256=d3I2mU3KAmxelY5JgSPqgioQ2aUgsZqnr6dFQr5kKXA,455
geopandas/tests/test_decorator.py,sha256=nC7qRe2kgR1X7nFO_5olgw09BmbhUAIVAvy5phBo3yA,1474
geopandas/tests/test_dissolve.py,sha256=X4haL2cbdCMrApYGMAgZdsyINZB4Jwu4hIUCs7vDlAk,11801
geopandas/tests/test_explore.py,sha256=s0va35t83iOiPOBf2i5BCsaoMDfk619SjNVIKNLAzIs,39317
geopandas/tests/test_extension_array.py,sha256=80b3GUGuDPX9F43D-wCxAMkYQg_zPH0HlOb2mSdQI2k,18452
geopandas/tests/test_geocode.py,sha256=9VZzWKrarkv59hCSt_6idz-8I_9y6FfVSr4IiPd6O-w,5039
geopandas/tests/test_geodataframe.py,sha256=QLUNYbzRXq3T96Corn_0q3IS92GPo9v9qOFo-3IO5Dk,63134
geopandas/tests/test_geom_methods.py,sha256=tCHgJCyGOm20iE-PZMYKigAiA1X7SJPFitAZ2kTzgNA,92924
geopandas/tests/test_geoseries.py,sha256=0QIjK4vox0wgQLex-VjhR5_EcV7agyhlFHawAfLvCdA,28838
geopandas/tests/test_merge.py,sha256=r8948zPMEH3ivFcYFxUN6fm7qHLUcRKliIcp5r4WHFQ,9908
geopandas/tests/test_op_output_types.py,sha256=bosP5TT_Ex3YrpFs3YNgkLbpHF1ikyYSop9o6QLK2M4,14569
geopandas/tests/test_overlay.py,sha256=-Pxno90K_T2sppCuB8spdZRjEtg6PlMUBGJFN1VFhNY,31977
geopandas/tests/test_pandas_accessor.py,sha256=QeZVE6dnaPxZfBJ_V7ZpnQNxd2HhpWSwmH2WE5tWdag,1688
geopandas/tests/test_pandas_methods.py,sha256=Dh8uwDVRWO3T54uTyg1WgryrNtSvsv-9oQH0ysGbHI8,28736
geopandas/tests/test_plotting.py,sha256=hbIWWQY87GRd51RZ8kZGLjziypKW40D36_vVN_M5lMs,77529
geopandas/tests/test_show_versions.py,sha256=3rFbaoagNkP-MirR1Xu2Nq1RT7b5si-OpFKeDO6ptD0,1173
geopandas/tests/test_sindex.py,sha256=jtW-9zX-0qAcCGhux6LjBIwVygiEA5fQ8Kf0fWjnOB8,35979
geopandas/tests/test_testing.py,sha256=6dOYHkLRSr4t7_WVycFsQeS5DeU_voHN07Bevfi4mbs,5707
geopandas/tests/test_types.py,sha256=DHZW8bRbt4j2bjsWVOircZVWsKunVSv22UW4dlsZjaM,2504
geopandas/tests/util.py,sha256=RF5FULP2_3lOxaIQA1S0NF15GO6aoyQGjb0UywraASY,4483
geopandas/tools/__init__.py,sha256=pqjdhW29m1QmYQRAXcKrjpPmlcufWnOLXZEyu8HRfDA,295
geopandas/tools/__pycache__/__init__.cpython-313.pyc,,
geopandas/tools/__pycache__/_random.cpython-313.pyc,,
geopandas/tools/__pycache__/_show_versions.cpython-313.pyc,,
geopandas/tools/__pycache__/clip.cpython-313.pyc,,
geopandas/tools/__pycache__/geocoding.cpython-313.pyc,,
geopandas/tools/__pycache__/hilbert_curve.cpython-313.pyc,,
geopandas/tools/__pycache__/overlay.cpython-313.pyc,,
geopandas/tools/__pycache__/sjoin.cpython-313.pyc,,
geopandas/tools/__pycache__/util.cpython-313.pyc,,
geopandas/tools/_random.py,sha256=WhZho_PkT_psUVXWzq5UlZcAJqn0leSeL9tF7PNLbxE,2621
geopandas/tools/_show_versions.py,sha256=Ul3CqJOH2E_HcIgN5Gqg5mwpRb-97_iOrJ2H5ec0ZAw,3795
geopandas/tools/clip.py,sha256=zI3qLR5nbfIoKupLSqbsC5CM1m3Fkxn-dC2NI97I2JM,9653
geopandas/tools/geocoding.py,sha256=CDxHbYutsPx_NxdAuym9yZDUBYsJC8Ik3i579P-aSmQ,5646
geopandas/tools/hilbert_curve.py,sha256=mam_fSLzVr_jI84P0MxtHdUan4KtwAHiAVmAExl9Qzk,4759
geopandas/tools/overlay.py,sha256=qc5Lv93owW-q9XnxCbvj5WOmCwkypsNCKwVKEV_OrWA,16947
geopandas/tools/sjoin.py,sha256=HbUgkM9A63uYT4GRZUge1hAIbHaeVIEM7Uq0_K4UnpY,25691
geopandas/tools/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
geopandas/tools/tests/__pycache__/__init__.cpython-313.pyc,,
geopandas/tools/tests/__pycache__/test_clip.cpython-313.pyc,,
geopandas/tools/tests/__pycache__/test_hilbert_curve.cpython-313.pyc,,
geopandas/tools/tests/__pycache__/test_random.cpython-313.pyc,,
geopandas/tools/tests/__pycache__/test_sjoin.cpython-313.pyc,,
geopandas/tools/tests/__pycache__/test_tools.cpython-313.pyc,,
geopandas/tools/tests/test_clip.py,sha256=ek-l8i0CH6NquaRSP2_7Y3cMjKhMGK2W8i70Q-JSqi4,18221
geopandas/tools/tests/test_hilbert_curve.py,sha256=JNlwQQjTB7GrzIi8yZtpKcur9O8scDI6pL2IQsSWGu0,2087
geopandas/tools/tests/test_random.py,sha256=F9O01PLyCxX07lptpOb5OmjpdjXsEP1_6Eyh39ZSTio,2501
geopandas/tools/tests/test_sjoin.py,sha256=blNbVbit54bAS7YM2nPf93YWLc08o2_cy37nY5l9_n0,50943
geopandas/tools/tests/test_tools.py,sha256=zFmiAHBNKbSsdSmKkRFCueW_Oysz_Rca8Dvvd9wsKPg,1483
geopandas/tools/util.py,sha256=_qZKkCnOSgNCgbH30LFsyQ3tWSKE050ozohwtjmE3Gg,1452
