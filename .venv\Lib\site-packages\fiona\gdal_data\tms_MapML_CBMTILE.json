{"type": "TileMatrixSetType", "identifier": "CBMTILE", "title": "Lambert Conformal Conic-based tiled coordinate reference system for Canada.", "supportedCRS": "http://www.opengis.net/def/crs/EPSG/0/3978", "tileMatrix": [{"matrixHeight": 5, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 5, "identifier": "0", "type": "TileMatrixType", "scaleDenominator": 137016643.08090523}, {"matrixHeight": 9, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 9, "identifier": "1", "type": "TileMatrixType", "scaleDenominator": 80320101.11639273}, {"matrixHeight": 15, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 15, "identifier": "2", "type": "TileMatrixType", "scaleDenominator": 47247118.303760424}, {"matrixHeight": 25, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 25, "identifier": "3", "type": "TileMatrixType", "scaleDenominator": 28348270.982256256}, {"matrixHeight": 42, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 42, "identifier": "4", "type": "TileMatrixType", "scaleDenominator": 16536491.40631615}, {"matrixHeight": 73, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 73, "identifier": "5", "type": "TileMatrixType", "scaleDenominator": 9449423.660752086}, {"matrixHeight": 121, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 121, "identifier": "6", "type": "TileMatrixType", "scaleDenominator": 5669654.196451251}, {"matrixHeight": 208, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 208, "identifier": "7", "type": "TileMatrixType", "scaleDenominator": 3307298.28126323}, {"matrixHeight": 363, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 363, "identifier": "8", "type": "TileMatrixType", "scaleDenominator": 1889884.732150417}, {"matrixHeight": 605, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 605, "identifier": "9", "type": "TileMatrixType", "scaleDenominator": 1133930.8392902503}, {"matrixHeight": 1036, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 1036, "identifier": "10", "type": "TileMatrixType", "scaleDenominator": 661459.656252646}, {"matrixHeight": 1727, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 1727, "identifier": "11", "type": "TileMatrixType", "scaleDenominator": 396875.79375158757}, {"matrixHeight": 2900, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 2900, "identifier": "12", "type": "TileMatrixType", "scaleDenominator": 236235.59151880213}, {"matrixHeight": 5000, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 5000, "identifier": "13", "type": "TileMatrixType", "scaleDenominator": 137016.64308090523}, {"matrixHeight": 8530, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 8530, "identifier": "14", "type": "TileMatrixType", "scaleDenominator": 80320.10111639272}, {"matrixHeight": 14501, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 14501, "identifier": "15", "type": "TileMatrixType", "scaleDenominator": 47247.11830376043}, {"matrixHeight": 24167, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 24167, "identifier": "16", "type": "TileMatrixType", "scaleDenominator": 28348.270982256254}, {"matrixHeight": 41429, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 41429, "identifier": "17", "type": "TileMatrixType", "scaleDenominator": 16536.49140631615}, {"matrixHeight": 72500, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 72500, "identifier": "18", "type": "TileMatrixType", "scaleDenominator": 9449.423660752085}, {"matrixHeight": 120834, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 120834, "identifier": "19", "type": "TileMatrixType", "scaleDenominator": 5669.654196451251}, {"matrixHeight": 207143, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 207143, "identifier": "20", "type": "TileMatrixType", "scaleDenominator": 3307.29828126323}, {"matrixHeight": 362501, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 362501, "identifier": "21", "type": "TileMatrixType", "scaleDenominator": 1889.884732150417}, {"matrixHeight": 604167, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 604167, "identifier": "22", "type": "TileMatrixType", "scaleDenominator": 1133.93083929025}, {"matrixHeight": 1035715, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 1035715, "identifier": "23", "type": "TileMatrixType", "scaleDenominator": 661.4596562526459}, {"matrixHeight": 1726191, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 1726191, "identifier": "24", "type": "TileMatrixType", "scaleDenominator": 396.87579375158754}, {"matrixHeight": 2900001, "tileHeight": 256, "tileWidth": 256, "topLeftCorner": [-34655800, 39310000], "matrixWidth": 2900001, "identifier": "25", "type": "TileMatrixType", "scaleDenominator": 236.23559151880212}]}